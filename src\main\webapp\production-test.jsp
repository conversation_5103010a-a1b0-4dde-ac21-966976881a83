<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>产线管理测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">产线管理测试页面</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>路由测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="${pageContext.request.contextPath}/production/debug" class="btn btn-primary" target="_blank">
                                测试调试接口
                            </a>
                            <a href="${pageContext.request.contextPath}/production/list" class="btn btn-success">
                                访问产线管理页面
                            </a>
                            <a href="${pageContext.request.contextPath}/production/1" class="btn btn-info" target="_blank">
                                测试产线详情页面
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>AJAX测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-warning" onclick="testGetProductionLine()">
                                测试获取产线信息
                            </button>
                            <button class="btn btn-secondary" onclick="testAddProductionLine()">
                                测试添加产线
                            </button>
                        </div>
                        <div id="testResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>系统信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Context Path:</strong> ${pageContext.request.contextPath}</p>
                        <p><strong>Server Info:</strong> ${pageContext.servletContext.serverInfo}</p>
                        <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function testGetProductionLine() {
        $('#testResult').html('<div class="alert alert-info">正在测试...</div>');
        
        $.ajax({
            url: '${pageContext.request.contextPath}/production/get/1',
            type: 'GET',
            success: function(response) {
                if (response) {
                    $('#testResult').html(
                        '<div class="alert alert-success">' +
                        '<strong>获取产线信息成功:</strong><br>' +
                        'ID: ' + response.id + '<br>' +
                        '名称: ' + response.name + '<br>' +
                        '状态: ' + response.status + '<br>' +
                        '更新时间: ' + response.lastUpdated +
                        '</div>'
                    );
                } else {
                    $('#testResult').html('<div class="alert alert-warning">未找到产线信息</div>');
                }
            },
            error: function(xhr, status, error) {
                $('#testResult').html(
                    '<div class="alert alert-danger">' +
                    '<strong>请求失败:</strong><br>' +
                    'Status: ' + xhr.status + '<br>' +
                    'Error: ' + error +
                    '</div>'
                );
            }
        });
    }
    
    function testAddProductionLine() {
        $('#testResult').html('<div class="alert alert-info">正在测试添加产线...</div>');
        
        $.ajax({
            url: '${pageContext.request.contextPath}/production/add',
            type: 'POST',
            data: {
                name: '测试产线_' + new Date().getTime(),
                status: '运行中'
            },
            success: function(response) {
                if (response === 'success') {
                    $('#testResult').html('<div class="alert alert-success">添加产线成功</div>');
                } else {
                    $('#testResult').html('<div class="alert alert-warning">添加产线失败: ' + response + '</div>');
                }
            },
            error: function(xhr, status, error) {
                $('#testResult').html(
                    '<div class="alert alert-danger">' +
                    '<strong>添加产线失败:</strong><br>' +
                    'Status: ' + xhr.status + '<br>' +
                    'Error: ' + error +
                    '</div>'
                );
            }
        });
    }
    </script>
</body>
</html>
