SET SQL_SAFE_UPDATES = false;-- 创建数据库
CREATE DATABASE IF NOT EXISTS building_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- 使用数据库
USE building_db; -- 创建用户表
CREATE TABLE IF NOT EXISTS user (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    real_name VARCHAR(50) NOT NULL,
    role VARCHAR(20) NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_time TIMESTAMP NULL
);

-- 创建房间表
CREATE TABLE IF NOT EXISTS room (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_number VARCHAR(20) NOT NULL UNIQUE,
    floor_number INT NOT NULL,
    room_type VARCHAR(50) NOT NULL,
    area DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT '空闲中',
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建使用记录表
CREATE TABLE IF NOT EXISTS usage_record (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_id INT NOT NULL,
    user_id INT NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NULL,
    purpose VARCHAR(200) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT '使用中',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES room(id),
    FOREIGN KEY (user_id) REFERENCES user(id)
); -- 插入默认管理员账号
INSERT INTO user (username, password, real_name, role) 
VALUES ('admin', 'admin123', '系统管理员', 'admin');

-- 插入一些测试房间数据
INSERT INTO room (room_number, floor_number, room_type, area, status, description) VALUES
('101', 1, '办公室', 20.00, '空闲中', '一楼办公室'),
('102', 1, '会议室', 30.00, '空闲中', '一楼会议室'),
('201', 2, '实验室', 50.00, '空闲中', '二楼实验室'),
('202', 2, '办公室', 25.00, '空闲中', '二楼办公室'),
('301', 3, '会议室', 40.00, '空闲中', '三楼会议室'); -- 创建设备表
CREATE TABLE IF NOT EXISTS device (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '设备名称',
    type VARCHAR(50) NOT NULL COMMENT '设备类型',
    location VARCHAR(100) NOT NULL COMMENT '设备位置',
    status VARCHAR(20) NOT NULL DEFAULT '空闲' COMMENT '设备状态：空闲/使用中/维修中/已报废',
    last_maintenance_date DATE COMMENT '最后维护日期',
    next_maintenance_date DATE COMMENT '下次维护日期',
    manufacturer VARCHAR(100) COMMENT '制造商',
    model VARCHAR(100) COMMENT '型号',
    serial_number VARCHAR(100) COMMENT '序列号',
    description TEXT COMMENT '设备描述',

    -- 新增运行参数相关字段
    ip_address VARCHAR(50) COMMENT 'IP地址',
    mac_address VARCHAR(50) COMMENT 'MAC地址',
    power_status INT DEFAULT 0 COMMENT '电源状态：0-关闭 1-开启',
    temperature DOUBLE DEFAULT 0 COMMENT '温度',
    humidity DOUBLE DEFAULT 0 COMMENT '湿度',
    power_consumption DOUBLE DEFAULT 0 COMMENT '功耗',
    runtime INT DEFAULT 0 COMMENT '运行时间（小时）',
    firmware_version VARCHAR(50) COMMENT '固件版本',
    last_online_time DATETIME COMMENT '最后在线时间',
    connection_status INT DEFAULT 0 COMMENT '连接状态：0-离线 1-在线',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备信息表';

-- 插入测试数据
INSERT INTO device (
    name, type, location, status,
    last_maintenance_date, next_maintenance_date,
    manufacturer, model, serial_number, description,
    ip_address, mac_address, power_status, temperature, humidity,
    power_consumption, runtime, firmware_version, last_online_time, connection_status
) VALUES
-- 在线设备
('智能投影仪', '多媒体设备', '教室A101', '使用中',
 '2024-03-15', '2024-06-15',
 '爱普生', 'EB-L735U', 'EPS-2024-001', '激光高清投影仪',
 '*************', 'AA:BB:CC:11:22:33', 1, 38.5, 45.2,
 280.5, 1250, 'v2.5.1', NOW(), 1),

('中央空调', '环境设备', '教室A101', '使用中',
 '2024-02-20', '2024-05-20',
 '格力', 'GMV-H280WL/A', 'GR-2024-002', '变频中央空调',
 '*************', 'AA:BB:CC:11:22:34', 1, 42.1, 38.7,
 1850.3, 3600, 'v3.1.0', NOW(), 1),

('智能讲台', '教学设备', '教室A101', '使用中',
 '2024-03-01', '2024-06-01',
 '鸿合', 'HZ-900X', 'HH-2024-003', '多功能智能讲台',
 '*************', 'AA:BB:CC:11:22:35', 1, 36.8, 42.5,
 320.7, 2100, 'v1.8.2', NOW(), 1),

('电子白板', '教学设备', '教室B201', '空闲',
 '2024-03-10', '2024-06-10',
 '希沃', 'FB860', 'SW-2024-004', '交互式电子白板',
 '192.168.1.104', 'AA:BB:CC:11:22:36', 1, 35.2, 44.8,
 180.5, 980, 'v2.2.0', NOW(), 1),

('监控摄像头', '安防设备', '教室B201', '使用中',
 '2024-02-25', '2024-05-25',
 '海康威视', 'DS-2CD2T85G1-I5', 'HK-2024-005', '800万像素监控摄像头',
 '192.168.1.105', 'AA:BB:CC:11:22:37', 1, 32.6, 46.3,
 12.8, 4500, 'v3.4.5', NOW(), 1),

('智能照明', '环境设备', '教室B201', '使用中',
 '2024-03-05', '2024-06-05',
 '飞利浦', 'Hue White', 'PH-2024-006', '智能LED照明系统',
 '192.168.1.106', 'AA:BB:CC:11:22:38', 1, 40.3, 43.1,
 85.2, 3200, 'v2.0.1', NOW(), 1),

('服务器', '网络设备', '机房C101', '使用中',
 '2024-02-15', '2024-05-15',
 '戴尔', 'PowerEdge R740', 'DL-2024-007', '高性能机架式服务器',
 '192.168.1.107', 'AA:BB:CC:11:22:39', 1, 45.8, 40.2,
 420.6, 5600, 'v4.1.2', NOW(), 1),

('网络交换机', '网络设备', '机房C101', '使用中',
 '2024-03-20', '2024-06-20',
 '华为', 'S5735-L24P4S-A1', 'HW-2024-008', '千兆以太网交换机',
 '192.168.1.108', 'AA:BB:CC:11:22:40', 1, 39.7, 42.8,
 65.3, 4800, 'v5.2.0', NOW(), 1),

('无线AP', '网络设备', '走廊D101', '使用中',
 '2024-02-28', '2024-05-28',
 'TP-Link', 'EAP245', 'TP-2024-009', '企业级无线接入点',
 '192.168.1.109', 'AA:BB:CC:11:22:41', 1, 37.5, 45.6,
 18.2, 3900, 'v3.0.3', NOW(), 1),

('多媒体控制器', '多媒体设备', '报告厅E101', '使用中',
 '2024-03-12', '2024-06-12',
 'Extron', 'DTP CrossPoint 84', 'EX-2024-010', '4K矩阵切换器',
 '192.168.1.110', 'AA:BB:CC:11:22:42', 1, 41.2, 41.9,
 150.4, 2800, 'v2.7.1', NOW(), 1),

-- 离线设备
('显微镜', '实验设备', '实验室A101', '空闲',
 '2024-02-15', '2024-05-15',
 '奥林巴斯', 'CX23', 'OLY-2024-001', '生物显微镜',
 '192.168.1.111', 'AA:BB:CC:11:22:43', 0, 0, 0,
 0, 850, 'v1.0.0', '2024-04-10 15:30:00', 0),

('投影仪', '教学设备', '教室B203', '空闲',
 '2024-02-20', '2024-05-20',
 '爱普生', 'CB-X05', 'EPS-2024-002', '教学用投影仪',
 '192.168.1.112', 'AA:BB:CC:11:22:44', 0, 0, 0,
 0, 1200, 'v1.5.2', '2024-04-08 09:15:00', 0),

('打印机', '办公设备', '办公室C305', '空闲',
 '2024-01-10', '2024-04-10',
 '惠普', 'LaserJet', 'HP-2024-003', '激光打印机',
 '192.168.1.113', 'AA:BB:CC:11:22:45', 0, 0, 0,
 0, 950, 'v2.3.4', '2024-04-11 10:30:00', 0),

('空调', '其他', '会议室D401', '空闲',
 '2024-02-01', '2024-05-01',
 '格力', 'KFR-35', 'GREE-2024-004', '中央空调',
 '192.168.1.114', 'AA:BB:CC:11:22:46', 0, 0, 0,
 0, 2500, 'v2.1.0', '2024-04-09 14:20:00', 0),

('电脑', '办公设备', '实验室A102', '空闲',
 '2024-02-25', '2024-05-25',
 '联想', 'ThinkPad', 'LEN-2024-005', '实验用电脑',
 '192.168.1.115', 'AA:BB:CC:11:22:47', 0, 0, 0,
 0, 1800, 'v3.0.1', '2024-04-07 16:45:00', 0);/*
 * 预约表
*/
CREATE TABLE reservation (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_id INT,
    device_id INT,
    user_id INT NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    purpose VARCHAR(500) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT '待审核',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES room(id),
    FOREIGN KEY (device_id) REFERENCES device(id),
    FOREIGN KEY (user_id) REFERENCES user(id),
    CHECK (
        (room_id IS NOT NULL AND device_id IS NULL) OR 
        (room_id IS NULL AND device_id IS NOT NULL)
    )
);CREATE TABLE production_line (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入示例数据
INSERT INTO production_line (name, status) VALUES
('产线A', '运行中'),
('产线B', '维护中'); -- 系统设置表创建脚本
-- 创建日期: 2024年6月12日
-- 描述: 用于存储系统各项设置，包括安全设置、邮件设置、备份设置和通知设置


-- 创建系统设置表
CREATE TABLE `system_settings` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 安全设置
  `min_password_length` INT NOT NULL DEFAULT 8 COMMENT '最小密码长度',
  `require_uppercase` INT NOT NULL DEFAULT 1 COMMENT '是否要求大写字母',
  `require_numbers` INT NOT NULL DEFAULT 1 COMMENT '是否要求数字',
  `require_special_chars` INT NOT NULL DEFAULT 0 COMMENT '是否要求特殊字符',
  `max_login_attempts` INT NOT NULL DEFAULT 5 COMMENT '最大登录尝试次数',
  
  -- 邮件设置
  `smtp_server` VARCHAR(255) DEFAULT NULL COMMENT 'SMTP服务器地址',
  `smtp_port` INT DEFAULT 587 COMMENT 'SMTP服务器端口',
  `sender_email` VARCHAR(255) DEFAULT NULL COMMENT '发送者邮箱',
  `email_password` VARCHAR(255) DEFAULT NULL COMMENT '邮箱密码（应加密存储）',
  
  -- 备份设置
  `auto_backup` INT NOT NULL DEFAULT 0 COMMENT '是否自动备份',
  `backup_frequency` VARCHAR(20) DEFAULT 'daily' COMMENT '备份频率（daily/weekly/monthly）',
  `backup_retention` INT DEFAULT 30 COMMENT '备份保留天数',
  `backup_path` VARCHAR(255) DEFAULT NULL COMMENT '备份存储路径',
  `last_backup_time` DATETIME DEFAULT NULL COMMENT '上次备份时间',
  
  -- 通知设置
  `system_notifications` INT NOT NULL DEFAULT 1 COMMENT '是否启用系统通知',
  `email_notifications` INT NOT NULL DEFAULT 0 COMMENT '是否启用邮件通知',
  `notify_reservation` INT NOT NULL DEFAULT 1 COMMENT '是否通知预约活动',
  `notify_maintenance` INT NOT NULL DEFAULT 1 COMMENT '是否通知维护活动',
  `notify_system` INT NOT NULL DEFAULT 1 COMMENT '是否通知系统事件',
  
  -- 时间戳
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 插入默认设置
INSERT INTO `system_settings` (
  `min_password_length`, `require_uppercase`, `require_numbers`, `require_special_chars`, `max_login_attempts`,
  `smtp_port`, `backup_frequency`, `backup_retention`,
  `system_notifications`, `notify_reservation`, `notify_maintenance`, `notify_system`
) VALUES (
  8, 1, 1, 0, 5,
  587, 'daily', 30,
  1, 1, 1, 1
); -- 备份日志表创建脚本
-- 创建日期: 2024年6月12日
-- 描述: 用于记录系统备份的历史记录，包括备份时间、文件名、状态等信息


-- 创建备份日志表
CREATE TABLE `backup_logs` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `backup_time` DATETIME NOT NULL COMMENT '备份时间',
  `backup_file` VARCHAR(255) NOT NULL COMMENT '备份文件名',
  `backup_path` VARCHAR(255) NOT NULL COMMENT '备份文件路径',
  `backup_size` BIGINT DEFAULT NULL COMMENT '备份文件大小(字节)',
  `backup_type` VARCHAR(20) NOT NULL COMMENT '备份类型(manual/scheduled)',
  `status` VARCHAR(20) NOT NULL COMMENT '备份状态(success/failed)',
  `error_message` TEXT DEFAULT NULL COMMENT '错误信息(如果失败)',
  `duration` INT DEFAULT NULL COMMENT '备份耗时(秒)',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_backup_time` (`backup_time`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='备份日志表'; 
-- 备份任务调度表创建脚本
-- 创建日期: 2024年6月12日
-- 描述: 用于存储备份任务的调度信息，支持定时备份功能


-- 创建备份任务调度表
CREATE TABLE `backup_schedule` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `schedule_name` VARCHAR(100) NOT NULL COMMENT '调度任务名称',
  `active` INT NOT NULL DEFAULT 1 COMMENT '是否激活',
  `schedule_type` VARCHAR(20) NOT NULL COMMENT '调度类型(daily/weekly/monthly)',
  `day_of_week` INT DEFAULT NULL COMMENT '星期几(1-7，仅weekly类型使用)',
  `day_of_month` INT DEFAULT NULL COMMENT '每月第几天(1-31，仅monthly类型使用)',
  `hour` INT NOT NULL COMMENT '小时(0-23)',
  `minute` INT NOT NULL COMMENT '分钟(0-59)',
  `retention_days` INT NOT NULL DEFAULT 30 COMMENT '备份保留天数',
  `backup_path` VARCHAR(255) NOT NULL COMMENT '备份路径',
  `last_run_time` DATETIME DEFAULT NULL COMMENT '上次运行时间',
  `next_run_time` DATETIME DEFAULT NULL COMMENT '下次计划运行时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_schedule_name` (`schedule_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='备份任务调度表';

-- 插入默认的每日备份任务
INSERT INTO `backup_schedule` (
  `schedule_name`, `active`, `schedule_type`, 
  `hour`, `minute`, `retention_days`, `backup_path`
) VALUES (
  '每日凌晨备份', 0, 'daily', 
  2, 0, 30, 'C:/building/backup'
); -- 更新房间表，添加布局相关字段
ALTER TABLE room
ADD COLUMN capacity INT COMMENT '容纳人数' AFTER description,
ADD COLUMN shape VARCHAR(50) COMMENT '教室形状（矩形、L形等）' AFTER capacity,
ADD COLUMN width INT COMMENT '宽度（厘米）' AFTER shape,
ADD COLUMN length INT COMMENT '长度（厘米）' AFTER width,
ADD COLUMN position VARCHAR(255) COMMENT '在楼层中的位置（坐标，JSON格式）' AFTER length,
ADD COLUMN layout_image_url VARCHAR(255) COMMENT '布局图片URL' AFTER position;

-- 创建教室布局表
CREATE TABLE IF NOT EXISTS room_layout (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_id INT NOT NULL COMMENT '关联的教室ID',
    layout_data TEXT COMMENT '布局数据（JSON格式）',
    image_url VARCHAR(255) COMMENT '布局图片URL',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES room(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教室布局表';

-- 创建设备位置表
CREATE TABLE IF NOT EXISTS device_position (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL COMMENT '设备ID',
    room_id INT NOT NULL COMMENT '教室ID',
    pos_x DOUBLE NOT NULL COMMENT 'X坐标',
    pos_y DOUBLE NOT NULL COMMENT 'Y坐标',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES device(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES room(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备位置表';

-- 创建设备参数历史表
CREATE TABLE IF NOT EXISTS device_parameter_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL COMMENT '设备ID',
    parameter_name VARCHAR(50) NOT NULL COMMENT '参数名称',
    parameter_value VARCHAR(255) NOT NULL COMMENT '参数值',
    record_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    FOREIGN KEY (device_id) REFERENCES device(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备参数历史表';

-- 创建设备告警表
CREATE TABLE IF NOT EXISTS device_alert (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL COMMENT '设备ID',
    alert_type VARCHAR(50) NOT NULL COMMENT '告警类型',
    alert_level TINYINT NOT NULL COMMENT '告警级别：1-低 2-中 3-高',
    alert_message TEXT NOT NULL COMMENT '告警信息',
    is_resolved TINYINT DEFAULT 0 COMMENT '是否已解决：0-未解决 1-已解决',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    resolve_time TIMESTAMP NULL COMMENT '解决时间',
    FOREIGN KEY (device_id) REFERENCES device(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备告警表';

-- 创建设备维护记录表
CREATE TABLE IF NOT EXISTS device_maintenance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL COMMENT '设备ID',
    maintenance_type VARCHAR(50) NOT NULL COMMENT '维护类型',
    maintenance_desc TEXT NOT NULL COMMENT '维护描述',
    maintenance_result VARCHAR(50) NOT NULL COMMENT '维护结果',
    maintenance_person VARCHAR(50) NOT NULL COMMENT '维护人员',
    maintenance_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '维护时间',
    FOREIGN KEY (device_id) REFERENCES device(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备维护记录表';
-- 创建摄像头表
CREATE TABLE IF NOT EXISTS camera (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '摄像头名称',
    ip_address VARCHAR(50) NOT NULL COMMENT 'IP地址',
    port INT NOT NULL DEFAULT 554 COMMENT '端口号',
    username VARCHAR(50) COMMENT '用户名',
    password VARCHAR(50) COMMENT '密码',
    rtsp_url VARCHAR(255) COMMENT 'RTSP URL',
    location VARCHAR(100) COMMENT '位置',
    brand VARCHAR(50) COMMENT '品牌',
    model VARCHAR(50) COMMENT '型号',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-离线 1-在线',
    last_online_time DATETIME COMMENT '最后在线时间',
    room_id INT COMMENT '所属房间ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (room_id) REFERENCES room(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='摄像头信息表';

-- 创建人员进出记录表
CREATE TABLE IF NOT EXISTS person_record (
    id INT PRIMARY KEY AUTO_INCREMENT,
    camera_id INT NOT NULL COMMENT '摄像头ID',
    room_id INT NOT NULL COMMENT '房间ID',
    person_count INT NOT NULL DEFAULT 0 COMMENT '人数',
    record_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    image_url VARCHAR(255) COMMENT '图像URL',
    FOREIGN KEY (camera_id) REFERENCES camera(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES room(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员进出记录表';-- 为摄像头表添加流相关字段
ALTER TABLE camera 
ADD COLUMN stream_id VARCHAR(100) COMMENT '流ID，用于视频转码服务',
ADD COLUMN stream_format VARCHAR(20) DEFAULT 'hls' COMMENT '流格式：hls, flv',
ADD COLUMN stream_status TINYINT DEFAULT 0 COMMENT '流状态：0-未启动 1-运行中 2-错误',
ADD COLUMN stream_url VARCHAR(255) COMMENT '转码后的流URL',
ADD COLUMN last_stream_time DATETIME COMMENT '最后流活动时间',
ADD COLUMN stream_error_count INT DEFAULT 0 COMMENT '流错误次数',
ADD COLUMN auto_restart TINYINT DEFAULT 1 COMMENT '是否自动重启流：0-否 1-是';

-- 创建流日志表
CREATE TABLE IF NOT EXISTS stream_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    camera_id INT NOT NULL COMMENT '摄像头ID',
    stream_id VARCHAR(100) NOT NULL COMMENT '流ID',
    action VARCHAR(50) NOT NULL COMMENT '操作：start, stop, restart, error',
    message TEXT COMMENT '日志消息',
    error_details TEXT COMMENT '错误详情',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (camera_id) REFERENCES camera(id) ON DELETE CASCADE,
    INDEX idx_camera_id (camera_id),
    INDEX idx_stream_id (stream_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流操作日志表';

-- 更新现有摄像头数据，生成流ID
UPDATE camera 
SET stream_id = CONCAT('camera_', id),
    stream_format = 'hls'
WHERE stream_id IS NULL;

-- 添加索引优化查询性能
ALTER TABLE camera ADD INDEX idx_stream_id (stream_id);
ALTER TABLE camera ADD INDEX idx_stream_status (stream_status);
