package com.building.servlet;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.building.service.CameraService;
import com.building.service.impl.CameraServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 摄像头删除Servlet
 * 处理摄像头删除请求
 */
@WebServlet("/camera/delete")
public class CameraDeleteServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private CameraService cameraService;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        cameraService = new CameraServiceImpl();
        objectMapper = new ObjectMapper();
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取摄像头ID
            String idStr = request.getParameter("id");
            if (idStr == null || idStr.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "摄像头ID不能为空");
                objectMapper.writeValue(response.getWriter(), result);
                return;
            }

            int id = Integer.parseInt(idStr);
            
            // 检查摄像头是否存在
            if (cameraService.getCameraById(id) == null) {
                result.put("success", false);
                result.put("message", "摄像头不存在");
                objectMapper.writeValue(response.getWriter(), result);
                return;
            }

            // 执行删除操作
            boolean success = cameraService.deleteCamera(id);

            result.put("success", success);
            if (success) {
                result.put("message", "摄像头删除成功");
            } else {
                result.put("message", "摄像头删除失败");
            }
        } catch (NumberFormatException e) {
            result.put("success", false);
            result.put("message", "无效的摄像头ID格式");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
            e.printStackTrace();
        }

        objectMapper.writeValue(response.getWriter(), result);
    }
}
