<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="摄像头详情" />
    <jsp:param name="content" value="/WEB-INF/views/camera/detail-content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 摄像头详情卡片样式 */
        .camera-info-card {
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }
        .camera-info-card:hover {
            box-shadow: 0 0 20px rgba(0,0,0,0.15);
            transform: translateY(-3px);
        }
        .camera-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .camera-online {
            background-color: #28a745;
        }
        .camera-offline {
            background-color: #dc3545;
        }
        .camera-stream-container {
            height: 400px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
            border: 2px solid #333;
        }
        .camera-stream-container img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
        }
        .stream-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .stream-active {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, rgba(0, 100, 0, 0.1) 0%, rgba(0, 150, 0, 0.05) 100%);
        }
        .stream-placeholder {
            text-align: center;
        }
        .streaming-indicator {
            display: flex;
            justify-content: center;
            gap: 8px;
        }
        .streaming-indicator .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #28a745;
            animation: pulse 1.5s infinite;
        }
        .streaming-indicator .dot:nth-child(2) {
            animation-delay: 0.5s;
        }
        .streaming-indicator .dot:nth-child(3) {
            animation-delay: 1s;
        }
        @keyframes pulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        .camera-control-panel {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        .control-section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .direction-controls {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }
        .control-row {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border-width: 2px;
        }
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .empty-state {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px;
            border: 2px dashed #dee2e6;
        }
        .info-grid {
            display: grid;
            gap: 12px;
        }
        .info-item {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .info-item:hover {
            background: #e9ecef;
            transform: translateX(2px);
        }
        .info-label {
            font-size: 0.85rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }
        .info-value {
            font-size: 0.9rem;
            color: #212529;
            font-weight: 500;
        }
        .person-record-card {
            border-radius: 8px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        .person-record-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-color: #007bff;
        }
        .person-count-badge {
            font-size: 0.9rem;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
        }
    " />
    <jsp:param name="scripts" value="
        &lt;script src='https://cdn.jsdelivr.net/npm/chart.js'&gt;&lt;/script&gt;
        &lt;script&gt;
            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化图表
                initPersonCountChart();

                // 控制按钮点击事件
                document.querySelectorAll('.control-btn').forEach(btn =&gt; {
                    btn.addEventListener('click', function() {
                        const cameraId = this.getAttribute('data-camera-id');
                        const action = this.getAttribute('data-action');
                        controlCamera(cameraId, action);
                    });
                });

                // 连接/断开摄像头按钮点击事件
                const connectBtn = document.getElementById('connectBtn');
                if (connectBtn) {
                    connectBtn.addEventListener('click', function() {
                        const cameraId = this.getAttribute('data-camera-id');
                        const action = this.getAttribute('data-action');

                        fetch('${pageContext.request.contextPath}/camera/control', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'cameraId=' + cameraId + '&amp;action=' + action
                        })
                        .then(response =&gt; response.json())
                        .then(data =&gt; {
                            if (data.success) {
                                alert(data.message);
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('操作失败: ' + data.message);
                            }
                        })
                        .catch(error =&gt; {
                            console.error('Error:', error);
                            alert('操作失败，请稍后重试');
                        });
                    });
                }

                // 查看视频流按钮点击事件
                const viewStreamBtn = document.getElementById('viewStreamBtn');
                if (viewStreamBtn) {
                    viewStreamBtn.addEventListener('click', function() {
                        const cameraId = this.getAttribute('data-camera-id');
                        window.location.href = '${pageContext.request.contextPath}/camera/stream?id=' + cameraId;
                    });
                }
            });

            // 初始化人数统计图表
            function initPersonCountChart() {
                const ctx = document.getElementById('personCountChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: [
                                &lt;c:forEach items=\&quot;${personRecords}\&quot; var=\&quot;record\&quot; varStatus=\&quot;status\&quot;&gt;
                                    '${record.recordTime}'${!status.last ? ',' : ''}
                                &lt;/c:forEach&gt;
                            ],
                            datasets: [{
                                label: '人数',
                                data: [
                                    &lt;c:forEach items=\&quot;${personRecords}\&quot; var=\&quot;record\&quot; varStatus=\&quot;status\&quot;&gt;
                                        ${record.personCount}${!status.last ? ',' : ''}
                                    &lt;/c:forEach&gt;
                                ],
                                borderColor: 'rgba(75, 192, 192, 1)',
                                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            }
                        }
                    });
                }
            }

            // 控制摄像头
            function controlCamera(cameraId, action) {
                fetch('${pageContext.request.contextPath}/camera/control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&amp;action=' + action
                })
                .then(response =&gt; response.json())
                .then(data =&gt; {
                    if (data.success) {
                        alert(data.message);
                    } else {
                        alert('操作失败: ' + data.message);
                    }
                })
                .catch(error =&gt; {
                    console.error('Error:', error);
                    alert('操作失败，请稍后重试');
                });
            }

            // 删除摄像头按钮事件监听器
            const deleteCameraBtn = document.getElementById('deleteCameraBtn');
            if (deleteCameraBtn) {
                deleteCameraBtn.addEventListener('click', function() {
                    const cameraId = this.getAttribute('data-camera-id');
                    const cameraName = this.getAttribute('data-camera-name');

                    if (confirm('确定要删除摄像头 \&quot;' + cameraName + '\&quot; 吗？\\n\\n警告：此操作不可撤销！删除后将：\\n• 永久删除摄像头配置信息\\n• 停止所有相关的视频流\\n• 清除相关的历史记录')) {
                        fetch('${pageContext.request.contextPath}/camera/delete', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'id=' + cameraId
                        })
                        .then(response =&gt; response.json())
                        .then(data =&gt; {
                            if (data.success) {
                                alert('摄像头删除成功');
                                // 跳转回摄像头列表页面
                                window.location.href = '${pageContext.request.contextPath}/camera/list';
                            } else {
                                alert('删除失败: ' + (data.message || '未知错误'));
                            }
                        })
                        .catch(error =&gt; {
                            console.error('Error:', error);
                            alert('删除失败，请稍后重试');
                        });
                    }
                });
            }
        &lt;/script&gt;
    " />
</jsp:include>
