package com.building.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 调试控制器
 * 用于检查Spring容器状态和控制器映射情况
 */
@Controller
@RequestMapping("/debug")
public class DebugController {
    private static final Logger logger = LoggerFactory.getLogger(DebugController.class);

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 检查Spring容器状态
     */
    @GetMapping("/container")
    @ResponseBody
    public Map<String, Object> checkContainer() {
        logger.info("DebugController.checkContainer() 被调用");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取所有Controller bean
            String[] controllerBeans = applicationContext.getBeanNamesForType(Controller.class);
            result.put("controllerBeans", Arrays.asList(controllerBeans));
            
            // 获取所有bean名称
            String[] allBeans = applicationContext.getBeanDefinitionNames();
            result.put("totalBeans", allBeans.length);
            
            // 检查特定的控制器是否存在
            boolean systemControllerExists = applicationContext.containsBean("systemController");
            boolean productionControllerExists = applicationContext.containsBean("productionController");
            
            result.put("systemControllerExists", systemControllerExists);
            result.put("productionControllerExists", productionControllerExists);
            
            // 检查服务是否存在
            boolean systemServiceExists = applicationContext.containsBean("systemService");
            boolean productionServiceExists = applicationContext.containsBean("productionService");
            
            result.put("systemServiceExists", systemServiceExists);
            result.put("productionServiceExists", productionServiceExists);
            
            result.put("success", true);
            result.put("message", "Spring容器状态检查完成");
            
            logger.info("Spring容器状态检查完成，控制器数量: {}", controllerBeans.length);
            
        } catch (Exception e) {
            logger.error("检查Spring容器状态失败", e);
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试系统设置页面映射
     */
    @GetMapping("/test-system")
    @ResponseBody
    public Map<String, Object> testSystemMapping() {
        logger.info("DebugController.testSystemMapping() 被调用");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查SystemController是否存在
            Object systemController = applicationContext.getBean("systemController");
            if (systemController != null) {
                result.put("systemControllerFound", true);
                result.put("systemControllerClass", systemController.getClass().getName());
            } else {
                result.put("systemControllerFound", false);
            }
            
            result.put("success", true);
            result.put("message", "系统控制器映射测试完成");
            
        } catch (Exception e) {
            logger.error("测试系统控制器映射失败", e);
            result.put("success", false);
            result.put("message", "测试失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试产线管理页面映射
     */
    @GetMapping("/test-production")
    @ResponseBody
    public Map<String, Object> testProductionMapping() {
        logger.info("DebugController.testProductionMapping() 被调用");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查ProductionController是否存在
            Object productionController = applicationContext.getBean("productionController");
            if (productionController != null) {
                result.put("productionControllerFound", true);
                result.put("productionControllerClass", productionController.getClass().getName());
            } else {
                result.put("productionControllerFound", false);
            }
            
            result.put("success", true);
            result.put("message", "产线控制器映射测试完成");
            
        } catch (Exception e) {
            logger.error("测试产线控制器映射失败", e);
            result.put("success", false);
            result.put("message", "测试失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @ResponseBody
    public Map<String, Object> health() {
        logger.info("DebugController.health() 被调用");
        
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        result.put("message", "调试控制器运行正常");
        
        return result;
    }
}
